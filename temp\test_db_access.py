# -*- coding: utf-8 -*-
"""测试数据库访问是否正常"""
import sys
import os
# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)  # 回到 salary_changes 目录
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

from src.modules.data_storage.database_manager import DatabaseManager
from src.core.config_manager import ConfigManager

def test_database_access():
    """测试数据库访问"""
    print("=" * 60)
    print("测试数据库访问")
    print("=" * 60)
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 获取数据库配置
        db_config = config_manager.get_section("database")
        db_name = db_config.db_name if db_config else "salary_system.db"
        db_path = db_config.db_path if db_config else "data/db"
        
        print(f"配置的数据库名称: {db_name}")
        print(f"配置的数据库路径: {db_path}")
        
        # 初始化数据库管理器，直接指定数据库路径
        from pathlib import Path
        db_full_path = Path.cwd() / db_path / db_name
        db_manager = DatabaseManager(db_path=db_full_path)
        
        print(f"实际数据库路径: {db_manager.db_path}")
        print(f"数据库文件存在: {db_manager.db_path.exists()}")
        
        # 测试查询
        result = db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5")
        
        print(f"\n数据库中的表（前5个）:")
        if result:
            for row in result:
                print(f"  - {row['name'] if isinstance(row, dict) else row[0]}")
        else:
            print("  (无结果)")
        
        # 测试异动表数据
        result = db_manager.execute_query("""
            SELECT COUNT(*) FROM change_data_2025_12_active_employees
        """)
        if result:
            count = result[0]['COUNT(*)'] if isinstance(result[0], dict) else result[0][0]
            print(f"\nchange_data_2025_12_active_employees 表记录数: {count}")
        else:
            print(f"\nchange_data_2025_12_active_employees 表记录数: 0")
        
        # 测试数据内容
        result = db_manager.execute_query("""
            SELECT 工号, 姓名 FROM change_data_2025_12_active_employees LIMIT 3
        """)
        print(f"\n前3条记录的工号和姓名:")
        if result:
            for row in result:
                if isinstance(row, dict):
                    print(f"  工号: {row.get('工号', 'N/A')}, 姓名: {row.get('姓名', 'N/A')}")
                else:
                    print(f"  工号: {row[0]}, 姓名: {row[1]}")
        else:
            print("  (无数据)")
        
        print("\n✅ 数据库访问测试通过!")
        return True
        
    except Exception as e:
        print(f"\n❌ 数据库访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_database_access()