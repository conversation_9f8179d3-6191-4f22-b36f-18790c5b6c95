# -*- coding: utf-8 -*-
"""完整测试异动表导入流程"""
import sys
import os
import pandas as pd

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

def test_full_import():
    """测试完整的异动表导入流程"""
    print("=" * 60)
    print("测试完整的异动表导入流程")
    print("=" * 60)
    
    # 创建测试数据，模拟Excel中的多行表头
    test_data = {
        '序号': [1, 2, 3],
        '工号': ['19289006', '19339009', '19999999'],
        '姓名': ['测试员工A', '测试员工B', '测试员工C'],
        '部门名称': ['离休部', '离休部', '离休部'],
        '基本\n离休费': [5000, 6000, 7000],
        '结余\n津贴': [1000, 1200, 1500],
        '生活\n补贴': [800, 900, 1000],
        '住房\n补贴': [1500, 1600, 1700],
        '物业\n补贴': [300, 350, 400],
        '离休\n补贴': [2000, 2200, 2400],
        '增发一次\n性生活补贴': [500, 600, 700],
        '节日费': [1000, 1000, 1000],
        '合计': [12100, 13850, 15700],
        '备注': ['测试', '测试', '测试']
    }
    
    df = pd.DataFrame(test_data)
    print(f"\n创建测试DataFrame (模拟Excel导入):")
    print(f"原始列名 (包含换行符): {list(df.columns)[:5]}...")
    print(f"数据行数: {len(df)}")
    
    # 使用DynamicTableManager保存数据
    manager = DynamicTableManager()
    table_name = "change_data_2025_12_test_import"
    
    print(f"\n开始导入到表: {table_name}")
    
    try:
        # 调用保存方法
        success, message = manager.save_dataframe_to_table(
            df=df,
            table_name=table_name
        )
        
        if success:
            print(f"✓ 导入成功!")
            
            # 验证数据
            print(f"\n验证导入的数据:")
            import sqlite3
            conn = sqlite3.connect('data/db/salary_system.db')
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"  表的列数: {len(columns)}")
            
            # 检查是否有换行符
            has_newline = False
            for col in columns:
                col_name = col[1]
                if '\n' in col_name or '\r' in col_name:
                    print(f"  ⚠️ 列名仍包含换行符: {repr(col_name)}")
                    has_newline = True
            
            if not has_newline:
                print(f"  ✓ 所有列名已正确清理")
            
            # 检查数据行数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  导入的数据行数: {count}")
            
            # 检查数据内容
            cursor.execute(f"SELECT 工号, 姓名, 基本离休费, 合计 FROM {table_name}")
            rows = cursor.fetchall()
            print(f"\n  数据内容预览:")
            for row in rows:
                print(f"    工号: {row[0]}, 姓名: {row[1]}, 基本离休费: {row[2]}, 合计: {row[3]}")
            
            # 清理测试表
            print(f"\n清理测试表...")
            cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
            conn.commit()
            conn.close()
            
            return True
        else:
            print(f"❌ 导入失败")
            return False
            
    except Exception as e:
        print(f"❌ 导入过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        print("开始完整的异动表导入测试...\n")
        
        if test_full_import():
            print("\n" + "=" * 60)
            print("✅ 异动表导入测试成功！")
            print("修复内容总结:")
            print("1. 添加了专门的列名清理方法处理换行符")
            print("2. 修复了异动表导入时的列名处理逻辑")
            print("3. 清理了数据库中已存在的错误列名")
            print("4. 现在异动表可以正常导入并显示数据")
            print("=" * 60)
        else:
            print("\n❌ 测试失败，请检查错误信息")
            
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()