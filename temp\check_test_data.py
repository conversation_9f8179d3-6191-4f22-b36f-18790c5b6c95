# -*- coding: utf-8 -*-
"""检查测试表的数据"""
import sys
import os
import sqlite3

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

def check_data():
    """检查测试表数据"""
    print("=" * 60)
    print("检查测试表数据")
    print("=" * 60)
    
    conn = sqlite3.connect('data/db/salary_system.db')
    cursor = conn.cursor()
    
    table_name = "change_data_2025_12_test_import"
    
    # 获取数据
    cursor.execute(f"SELECT * FROM {table_name}")
    rows = cursor.fetchall()
    
    print(f"\n找到 {len(rows)} 行数据")
    
    if rows:
        # 获取列名
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        col_names = [col[1] for col in columns]
        
        # 显示关键字段
        key_fields = ['employee_id', '工号', '姓名', '部门名称', '基本离休费', '合计']
        
        print("\n关键字段数据:")
        print("-" * 60)
        
        for i, row in enumerate(rows, 1):
            print(f"\n第 {i} 行:")
            row_dict = dict(zip(col_names, row))
            for field in key_fields:
                if field in row_dict:
                    value = row_dict[field]
                    print(f"  {field}: {value}")
    
    conn.close()

if __name__ == "__main__":
    check_data()