# -*- coding: utf-8 -*-
import sqlite3
import sys
sys.stdout.reconfigure(encoding='utf-8')

# 连接数据库
conn = sqlite3.connect('data/db/salary_system.db')
cursor = conn.cursor()

# 检查原始工资表的数据
print("检查 salary_data_2025_08_active_employees 表的数据：")
cursor.execute("""
    SELECT * FROM salary_data_2025_08_active_employees
    LIMIT 3
""")

rows = cursor.fetchall()
print(f"查询到 {len(rows)} 行数据")

# 获取列名
cursor.execute("PRAGMA table_info(salary_data_2025_08_active_employees)")
columns = cursor.fetchall()
col_names = [col[1] for col in columns]
print(f"\n列名: {col_names[:10]}...")  # 只显示前10个列名

# 显示数据
for i, row in enumerate(rows):
    print(f"\n第{i+1}行数据（前10列）:")
    for j in range(min(10, len(row))):
        print(f"  {col_names[j]}: {row[j]}")

conn.close()