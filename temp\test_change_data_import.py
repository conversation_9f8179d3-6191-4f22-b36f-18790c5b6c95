# -*- coding: utf-8 -*-
"""测试异动表数据导入的字段映射问题"""
import sys
import os
import pandas as pd
import json

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

def test_field_mapping():
    """测试字段映射逻辑"""
    print("=" * 60)
    print("测试异动表字段映射")
    print("=" * 60)
    
    # 创建测试数据 - 模拟Excel导入的数据
    test_data = {
        '序号': [1, 2, 3],
        '工号': ['001', '002', '003'],
        '姓名': ['张三', '李四', '王五'],
        '部门名称': ['部门A', '部门B', '部门C'],
        '2025年岗位工资': [5000, 6000, 5500],
        '2025年薪级工资': [3000, 3500, 3200],
        '应发工资': [8000, 9500, 8700]
    }
    
    df = pd.DataFrame(test_data)
    print(f"原始数据列: {df.columns.tolist()}")
    print(f"原始数据前3行:\n{df.head(3)}")
    
    # 初始化表管理器
    manager = DynamicTableManager()
    
    # 测试字段映射获取
    table_name = "change_data_2025_12_active_employees"
    excel_columns = df.columns.tolist()
    
    print(f"\n调用_get_import_field_mappings:")
    mappings = manager._get_import_field_mappings(table_name, excel_columns)
    print(f"获取到的映射: {json.dumps(mappings, ensure_ascii=False, indent=2)}")
    
    # 测试_clean_column_name函数
    print(f"\n测试_clean_column_name函数:")
    for col in df.columns:
        cleaned = manager._clean_column_name(col)
        print(f"  {col} -> {cleaned}")
    
    # 模拟save_dataframe_to_table的部分逻辑
    print(f"\n模拟字段映射过程:")
    df_copy = df.copy()
    
    # 应用字段映射
    if mappings:
        active_map = {k: v for k, v in mappings.items() if k in df_copy.columns}
        print(f"活跃映射: {active_map}")
        
        if active_map:
            df_copy.rename(columns=active_map, inplace=True)
            print(f"映射后的列: {df_copy.columns.tolist()}")
    
    # 清理列名
    final_column_map = {col: manager._clean_column_name(col) for col in df_copy.columns}
    print(f"\n列名清理映射: {final_column_map}")
    df_copy.rename(columns=final_column_map, inplace=True)
    print(f"最终列名: {df_copy.columns.tolist()}")
    
    return True

if __name__ == "__main__":
    test_field_mapping()