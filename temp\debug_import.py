# -*- coding: utf-8 -*-
"""调试异动表导入问题"""
import sys
import os
import pandas as pd
import traceback

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

def debug_import():
    """调试导入流程"""
    print("=" * 60)
    print("调试异动表导入")
    print("=" * 60)
    
    # 创建测试数据
    test_data = {
        '序号': [1],
        '工号': ['TEST001'],
        '姓名': ['测试员工'],
        '部门名称': ['测试部'],
        '基本\n工资': [5000],
        '合计': [5000]
    }
    
    df = pd.DataFrame(test_data)
    print(f"\n测试数据:")
    print(df)
    
    manager = DynamicTableManager()
    table_name = "change_data_2025_12_debug"
    
    print(f"\n开始导入到表: {table_name}")
    
    try:
        success, message = manager.save_dataframe_to_table(
            df=df,
            table_name=table_name
        )
        
        print(f"\n导入结果: success={success}")
        print(f"消息: {message}")
        
        # 清理测试表
        if success:
            import sqlite3
            conn = sqlite3.connect('data/db/salary_system.db')
            cursor = conn.cursor()
            cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
            conn.commit()
            conn.close()
            print(f"\n已清理测试表 {table_name}")
            
    except Exception as e:
        print(f"\n异常: {e}")
        print("\n详细异常:")
        traceback.print_exc()

if __name__ == "__main__":
    debug_import()