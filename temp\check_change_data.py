# -*- coding: utf-8 -*-
import sqlite3
import sys
sys.stdout.reconfigure(encoding='utf-8')

# 连接数据库
conn = sqlite3.connect('data/db/salary_system.db')
cursor = conn.cursor()

# 检查 change_data_2025_12_active_employees 表的数据
print("检查 change_data_2025_12_active_employees 表的数据：")
cursor.execute("""
    SELECT 工号, 姓名, 部门名称, `2025年岗位工资`, `2025年薪级工资`, 
           `2025年基础性绩效`, 应发工资
    FROM change_data_2025_12_active_employees
    LIMIT 5
""")

rows = cursor.fetchall()
print(f"查询到 {len(rows)} 行数据")
for row in rows:
    print(f"  工号:{row[0]}, 姓名:{row[1]}, 部门:{row[2]}, 岗位工资:{row[3]}, 薪级工资:{row[4]}, 基础绩效:{row[5]}, 应发:{row[6]}")

# 检查数据是否为空或None
print("\n检查数据质量:")
cursor.execute("""
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN 姓名 IS NULL OR 姓名 = '' THEN 1 ELSE 0 END) as empty_name,
        SUM(CASE WHEN `2025年岗位工资` IS NULL OR `2025年岗位工资` = '' THEN 1 ELSE 0 END) as empty_salary,
        SUM(CASE WHEN 应发工资 IS NULL OR 应发工资 = '' THEN 1 ELSE 0 END) as empty_total
    FROM change_data_2025_12_active_employees
""")
stats = cursor.fetchone()
print(f"总记录数: {stats[0]}")
print(f"姓名为空: {stats[1]}")
print(f"岗位工资为空: {stats[2]}") 
print(f"应发工资为空: {stats[3]}")

conn.close()