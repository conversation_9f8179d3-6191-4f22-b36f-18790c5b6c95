#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式渲染器

负责实际的数据格式化和渲染，将原始数据转换为用户友好的显示格式。
根据字段类型和格式配置，对数据进行类型转换、格式化和验证。

主要功能:
- 数据类型转换（字符串、整型、浮点型、货币等）
- 格式化渲染（千分位分隔符、小数位数、货币符号等）
- 空值处理和默认值设置
- 数据验证和错误处理
- 批量数据处理优化

支持的数据类型:
- currency: 货币格式（¥1,234.56）
- integer: 整型格式（1,234）
- float: 浮点型格式（1,234.56）
- percentage: 百分比格式（12.3%）
- date: 日期格式（2025年07月18日）
- string: 字符串格式

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, date
import re
import logging
from decimal import Decimal, InvalidOperation

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

from .format_config import FormatConfig
from .field_registry import FieldRegistry


class FormatRenderer:
    """
    格式渲染器
    
    负责将原始数据根据配置的格式规则转换为用户友好的显示格式。
    处理各种数据类型的格式化、验证和错误处理。
    """
    
    def __init__(self, format_config: FormatConfig, field_registry: FieldRegistry):
        """
        初始化格式渲染器
        
        Args:
            format_config: 格式配置管理器
            field_registry: 字段注册系统
        """
        self.logger = setup_logger("format_management.format_renderer")
        
        self.format_config = format_config
        self.field_registry = field_registry
        
        # 格式化缓存
        self._format_cache = {}
        
        # 错误统计
        self._error_count = 0
        self._warning_count = 0
        
        self.logger.info("🎨 [格式渲染] 格式渲染器初始化完成")
    
    # ================== 主要渲染接口 ==================
    
    def render_dataframe(self, df: pd.DataFrame, table_type: str) -> pd.DataFrame:
        """
        渲染整个DataFrame
        
        Args:
            df: 原始DataFrame
            table_type: 表格类型
            
        Returns:
            格式化后的DataFrame
        """
        try:
            if df.empty:
                return df
            
            # 创建副本以避免修改原始数据
            formatted_df = df.copy()
            
            # 🎯 [用户需求] 获取隐藏字段并从DataFrame中移除
            hidden_fields = self.field_registry.get_hidden_fields(table_type)
            self.logger.info(f"🎯 [格式渲染] 隐藏字段配置: {hidden_fields}")
            if hidden_fields:
                # 移除隐藏字段
                columns_to_drop = [col for col in hidden_fields if col in formatted_df.columns]
                if columns_to_drop:
                    formatted_df = formatted_df.drop(columns=columns_to_drop)
                    self.logger.info(f"🎯 [格式渲染] 已隐藏字段: {columns_to_drop}")
                else:
                    self.logger.info(f"🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）")
            
            # 获取字段类型映射（英文字段名 -> 类型）
            field_types = self.field_registry.get_table_field_types(table_type)
            
            # 获取字段映射（英文字段名 -> 中文显示名）并创建反向映射
            field_mappings = self.field_registry.get_table_mapping(table_type)
            # 创建反向映射：中文显示名 -> 英文字段名
            reverse_mappings = {chinese_name: english_name for english_name, chinese_name in field_mappings.items()}
            
            # 逐列格式化
            for column in formatted_df.columns:
                # [关键修复] 先通过反向映射获取英文字段名，再查找字段类型
                english_field_name = reverse_mappings.get(column, column)
                field_type = field_types.get(english_field_name, 'string')
                
                # [调试日志] 记录字段类型查找过程，特别关注用户需求的字段
                if column in ["增发一次性生活补贴", "补发", "借支", "备注", "月份"]:
                    self.logger.info(f"[关键修复] 字段: {column} -> 英文名: {english_field_name} -> 类型: {field_type}")
                
                try:
                    # 🔧 [P2-修复] 获取字段的中文显示名称，用于特殊字段识别
                    display_name = self.field_registry.get_display_name(column, table_type)
                    formatted_df[column] = self.render_column(
                        formatted_df[column], 
                        field_type, 
                        column, 
                        table_type,
                        display_name  # 传递显示名称，确保人员类别代码等特殊字段能正确处理
                    )
                except Exception as e:
                    self.logger.error(f"🎨 [格式渲染] 列格式化失败 {column}: {e}")
                    self._error_count += 1
                    # 保留原始数据
                    continue
            
            # 🎯 [用户需求] 按display_order重新排列列的顺序 + 🔧 [格式修复] 降级处理
            display_fields = self.field_registry.get_display_fields(table_type)
            self.logger.info(f"🔧 [DEBUG] table_type={table_type}, display_fields={len(display_fields) if display_fields else 0}个字段")

            # 🔧 [关键修复] 强化display_fields获取逻辑，确保能找到正确配置
            if not display_fields and table_type:
                # 尝试通过不同的表名变体获取display_fields
                possible_table_names = [
                    table_type,
                    table_type.replace('_', ''),
                    f"salary_data_2025_08_{table_type}",  # 尝试完整表名格式
                ]
                
                for alt_name in possible_table_names:
                    alt_display_fields = self.field_registry.get_display_fields(alt_name)
                    if alt_display_fields:
                        display_fields = alt_display_fields
                        self.logger.info(f"🔧 [智能修复] 通过备用表名 '{alt_name}' 找到display_fields: {len(display_fields)}个")
                        break

            if display_fields:
                # 🔧 [P0-关键修复] 改进字段排序逻辑，不删除业务字段
                current_columns = list(formatted_df.columns)
                ordered_columns = []
                processed_fields = set()

                self.logger.debug(f"🔧 [P0修复] 开始字段排序: display_fields={len(display_fields)}个, current_columns={len(current_columns)}个")
                self.logger.debug(f"🔧 [P0修复] display_fields前5个: {display_fields[:5]}")
                self.logger.debug(f"🔧 [P0修复] current_columns前5个: {current_columns[:5]}")

                # 第一步：按配置顺序添加已配置的字段
                matched_count = 0
                for i, field in enumerate(display_fields):
                    if field in current_columns and field not in processed_fields:
                        # 直接匹配
                        ordered_columns.append(field)
                        processed_fields.add(field)
                        matched_count += 1
                        self.logger.debug(f"🔧 [P0修复] 直接匹配 {i+1}/{len(display_fields)}: {field}")
                    else:
                        # 尝试通过字段映射查找对应字段名
                        try:
                            mapped_field = self._find_mapped_field_name(field, current_columns, table_type)
                            if mapped_field and mapped_field not in processed_fields:
                                ordered_columns.append(mapped_field)
                                processed_fields.add(mapped_field)
                                matched_count += 1
                                self.logger.debug(f"🔧 [P0修复] 字段映射成功 {i+1}/{len(display_fields)}: {field} -> {mapped_field}")
                            else:
                                self.logger.debug(f"🔧 [P0修复] 字段未找到或已处理 {i+1}/{len(display_fields)}: '{field}'")
                        except Exception as mapping_error:
                            self.logger.debug(f"🔧 [P0修复] 字段映射异常 {i+1}/{len(display_fields)}: {field} -> {mapping_error}")
                            # 尝试简单的字符串匹配
                            fallback_match = self._simple_string_match(field, current_columns)
                            if fallback_match and fallback_match not in processed_fields:
                                ordered_columns.append(fallback_match)
                                processed_fields.add(fallback_match)
                                matched_count += 1
                                self.logger.debug(f"🔧 [P0修复] 降级匹配成功 {i+1}/{len(display_fields)}: {field} -> {fallback_match}")

                self.logger.info(f"🔧 [P0修复] 配置字段匹配完成: 成功匹配 {matched_count}/{len(display_fields)} 个字段")

                # 🔧 [P0-关键] 第二步：添加所有未处理的业务字段（不删除）
                remaining_columns = [col for col in current_columns if col not in processed_fields]
                if remaining_columns:
                    # 不再进行二次过滤，直接添加所有剩余字段
                    # 因为系统字段（id, created_at等）已经在前面的hidden_fields处理中删除了
                    ordered_columns.extend(remaining_columns)
                    self.logger.info(f"🔧 [P0-关键修复] 保留未配置的业务字段: {len(remaining_columns)}个")
                    self.logger.debug(f"🔧 [P0-关键修复] 保留的字段: {remaining_columns[:10]}...")

                # 使用重新排序的列
                formatted_df = formatted_df[ordered_columns]
                self.logger.info(f"🔧 [P0修复] 字段重排完成: 原始{len(current_columns)}个 -> 最终{len(ordered_columns)}个字段")
            else:
                # 🔧 [格式修复] 降级处理：display_fields为空时的处理
                self.logger.warning(f"🔧 [格式修复] display_fields为空，table_type={table_type}，尝试获取默认配置")
                # 尝试获取默认字段配置
                default_fields = self._get_default_display_fields(table_type, formatted_df.columns)
                if default_fields:
                    existing_default_fields = [field for field in default_fields if field in formatted_df.columns]
                    if existing_default_fields:
                        formatted_df = formatted_df[existing_default_fields]
                        self.logger.info(f"🔧 [格式修复] 使用默认字段配置: {len(existing_default_fields)}个字段")
                    else:
                        self.logger.info(f"🔧 [格式修复] 默认字段配置无效，保持原始列顺序")
                else:
                    self.logger.info(f"🔧 [格式修复] 无默认配置，保持原始列顺序: {len(formatted_df.columns)}列")
            
            self.logger.info(f"🎨 [格式渲染] DataFrame格式化完成: {table_type}, 行数: {len(formatted_df)}, 列数: {len(formatted_df.columns)}")
            return formatted_df
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] DataFrame格式化失败: {e}")
            return df

    def _get_default_display_fields(self, table_type: str, available_columns: List[str]) -> List[str]:
        """
        🔧 [格式修复] 获取默认显示字段配置

        Args:
            table_type: 表类型
            available_columns: 可用的列名列表

        Returns:
            默认字段列表
        """
        try:
            # 定义各表类型的默认字段顺序
            default_configs = {
                'active_employees': [
                    "工号", "姓名", "部门名称", "人员类别", "人员类别代码",
                    "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴",
                    "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发",
                    "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"
                ],
                'a_grade_employees': [
                    "工号", "姓名", "部门名称", "人员类别", "人员类别代码",
                    "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补",
                    "2025年奖励性绩效预发", "补发", "借支", "应发工资",
                    "2025公积金", "保险扣款", "代扣代存养老保险"
                ],
                'retired_employees': [
                    "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴",
                    "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费",
                    "增发一次性生活补贴", "补发", "合计", "借支", "备注"
                ],
                'pension_employees': [
                    "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费",
                    "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴",
                    "住房补贴", "增资预付", "补发", "借支", "应发工资",
                    "公积", "保险扣款", "备注"
                ]
            }

            # 🔧 [异动表修复] 检查是否为异动表类型，使用特殊处理
            if table_type and ('change_data' in table_type or table_type.startswith('change_data')):
                # 异动表的默认字段配置
                change_data_fields = [
                    "工号", "姓名", "部门名称", "人员类别", "人员类别代码",
                    "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴",
                    "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发",
                    "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险",
                    "基本退休费", "离退休生活补贴", "护理费", "增资预付", "保险扣款"
                ]
                # 只返回实际存在的字段
                existing_fields = [field for field in change_data_fields if field in available_columns]
                self.logger.info(f"🔧 [异动表修复] 异动表默认配置匹配: {len(existing_fields)}/{len(change_data_fields)} 字段")
                return existing_fields

            # 获取对应的默认配置
            default_fields = default_configs.get(table_type, [])

            if default_fields:
                # 只返回实际存在的字段
                existing_fields = [field for field in default_fields if field in available_columns]
                self.logger.info(f"🔧 [格式修复] 默认配置匹配: {len(existing_fields)}/{len(default_fields)} 字段")
                return existing_fields
            else:
                # 如果没有预定义配置，返回前20个可用列（避免过多列）
                limited_columns = list(available_columns)[:20]
                self.logger.info(f"🔧 [格式修复] 使用前{len(limited_columns)}列作为默认配置")
                return limited_columns

        except Exception as e:
            self.logger.error(f"🔧 [格式修复] 获取默认字段配置失败: {e}")
            return []

    def render_column(self, 
                     column: pd.Series, 
                     field_type: str, 
                     field_name: str, 
                     table_type: str,
                     display_name: str = None) -> pd.Series:
        """
        渲染单个列
        
        Args:
            column: 原始列数据
            field_type: 字段类型
            field_name: 字段名
            table_type: 表格类型
            
        Returns:
            格式化后的列数据
        """
        try:
            # 获取格式配置 - 传递表类型信息
            format_config = self.format_config.get_format_rules(field_type, table_type)
            if not format_config:
                self.logger.warning(f"🎨 [格式渲染] 未找到格式配置: {field_type}")
                return column
            
            # 🔧 [P2-修复] 使用显示名称（如果有）来进行特殊字段识别
            effective_field_name = display_name or field_name
            
            # 根据字段类型选择渲染方法
            if field_type == 'currency':
                return self._render_currency_column(column, format_config, effective_field_name)
            elif field_type == 'integer':
                return self._render_integer_column(column, format_config, effective_field_name)
            elif field_type == 'float':
                return self._render_float_column(column, format_config, effective_field_name)
            elif field_type == 'percentage':
                return self._render_percentage_column(column, format_config, effective_field_name)
            elif field_type == 'date':
                return self._render_date_column(column, format_config, effective_field_name)
            elif field_type == 'string':
                return self._render_string_column(column, format_config, effective_field_name)
            elif field_type == 'month_string':
                return self._render_month_string_column(column, format_config, effective_field_name)
            elif field_type == 'month_string_extract_last_two':
                return self._render_month_string_extract_last_two_column(column, format_config, effective_field_name)
            elif field_type == 'year_string':
                return self._render_year_string_column(column, format_config, effective_field_name)
            else:
                self.logger.warning(f"🎨 [格式渲染] 未知字段类型: {field_type}")
                return column
                
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 列格式化失败 {field_name}: {e}")
            return column
    
    def render_value(self, 
                    value: Any, 
                    field_type: str, 
                    field_name: str = None,
                    table_type: str = None) -> str:
        """
        渲染单个值
        
        Args:
            value: 原始值
            field_type: 字段类型
            field_name: 字段名（可选）
            
        Returns:
            格式化后的字符串
        """
        try:
            # 获取格式配置 - 传递表类型信息
            format_config = self.format_config.get_format_rules(field_type, table_type)
            if not format_config:
                # 🎯 [用户需求] 没有格式配置时的默认处理
                if field_type == 'string':
                    # 字符串类型，空值显示为空白
                    if pd.isna(value) or value is None or str(value).strip().lower() in ['none', 'nan', 'null', '-']:
                        return ""
                    return str(value)
                elif field_type == 'float':
                    # 浮点类型，空值显示为"0.00"
                    if pd.isna(value) or value is None or str(value).strip() in ['', '-', 'nan', 'None']:
                        return "0.00"
                    try:
                        return f"{float(value):.2f}"
                    except:
                        return "0.00"
                else:
                    return str(value) if value is not None else ""
            
            # 根据字段类型选择渲染方法
            if field_type == 'currency':
                return self._render_currency_value(value, format_config)
            elif field_type == 'integer':
                return self._render_integer_value(value, format_config)
            elif field_type == 'float':
                return self._render_float_value(value, format_config, field_name)
            elif field_type == 'percentage':
                return self._render_percentage_value(value, format_config)
            elif field_type == 'date':
                return self._render_date_value(value, format_config)
            elif field_type == 'string':
                return self._render_string_value(value, format_config, field_name)
            elif field_type == 'month_string':
                return self._render_month_string_value(value, format_config)
            elif field_type == 'month_string_extract_last_two':
                return self._render_month_string_extract_last_two_value(value, format_config)
            elif field_type == 'year_string':
                return self._render_year_string_value(value, format_config)
            else:
                return str(value) if value is not None else ""
                
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 值格式化失败 {field_name}: {e}")
            return str(value) if value is not None else ""
    
    # ================== 货币类型渲染 ==================
    
    def _render_currency_column(self, 
                               column: pd.Series, 
                               format_config: Dict, 
                               field_name: str) -> pd.Series:
        """渲染货币类型列"""
        try:
            def format_currency(value):
                return self._render_currency_value(value, format_config)
            
            return column.apply(format_currency)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 货币列格式化失败 {field_name}: {e}")
            return column
    
    def _render_currency_value(self, value: Any, format_config: Dict) -> str:
        """渲染货币值"""
        try:
            # 🔧 [P1修复] 检查是否错误地传入了Series
            if isinstance(value, pd.Series):
                if len(value) == 1:
                    value = value.iloc[0]
                else:
                    return format_config.get('zero_display', '0.00')
            
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('zero_display', '0.00')
            
            # 转换为数值
            try:
                if isinstance(value, str):
                    # 清理字符串中的非数字字符
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return format_config.get('zero_display', '0.00')
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
            except (ValueError, TypeError):
                return format_config.get('zero_display', '0.00')
            
            # 格式化设置
            decimal_places = format_config.get('decimal_places', 2)
            thousand_separator = format_config.get('thousand_separator', ',')
            symbol = format_config.get('symbol', '¥')
            symbol_position = format_config.get('symbol_position', 'prefix')
            negative_format = format_config.get('negative_format', '-{symbol}{value}')
            
            # 格式化数值
            if numeric_value == 0:
                return format_config.get('zero_display', '0.00')
            
            # 格式化为指定小数位数
            formatted_value = f"{abs(numeric_value):,.{decimal_places}f}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            # 添加货币符号
            if symbol_position == 'prefix':
                result = f"{symbol}{formatted_value}"
            else:
                result = f"{formatted_value}{symbol}"
            
            # 处理负数
            if numeric_value < 0:
                result = negative_format.format(symbol=symbol, value=formatted_value)
            
            return result
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 货币值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 整型渲染 ==================
    
    def _render_integer_column(self, 
                              column: pd.Series, 
                              format_config: Dict, 
                              field_name: str) -> pd.Series:
        """渲染整型列"""
        try:
            def format_integer(value):
                return self._render_integer_value(value, format_config)
            
            return column.apply(format_integer)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 整型列格式化失败 {field_name}: {e}")
            return column
    
    def _render_integer_value(self, value: Any, format_config: Dict) -> str:
        """渲染整型值"""
        try:
            # 🔧 [P1修复] 检查是否错误地传入了Series
            if isinstance(value, pd.Series):
                if len(value) == 1:
                    value = value.iloc[0]
                else:
                    return format_config.get('zero_display', '0')
            
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('zero_display', '0')
            
            # 转换为整数
            try:
                if isinstance(value, str):
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return format_config.get('zero_display', '0')
                    numeric_value = int(float(cleaned_value))
                else:
                    numeric_value = int(float(value))
            except (ValueError, TypeError):
                return format_config.get('zero_display', '0')
            
            # 格式化设置
            thousand_separator = format_config.get('thousand_separator', ',')
            
            # 格式化数值
            if numeric_value == 0:
                return format_config.get('zero_display', '0')
            
            # 添加千分位分隔符
            formatted_value = f"{numeric_value:,}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 整型值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 浮点型渲染 ==================
    
    def _render_float_column(self, 
                            column: pd.Series, 
                            format_config: Dict, 
                            field_name: str) -> pd.Series:
        """渲染浮点型列"""
        try:
            def format_float(value):
                return self._render_float_value(value, format_config, field_name)
            
            return column.apply(format_float)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 浮点型列格式化失败 {field_name}: {e}")
            return column
    
    def _render_float_value(self, value: Any, format_config: Dict, field_name: str = None) -> str:
        """渲染浮点型值 - 增强空值处理"""
        try:
            # 🔧 [P1修复] 检查是否错误地传入了Series
            if isinstance(value, pd.Series):
                if len(value) == 1:
                    value = value.iloc[0]
                else:
                    decimal_places = format_config.get('decimal_places', 2)
                    return f"{0:.{decimal_places}f}"
            
            # 🎯 [用户需求] 扩展空值处理：None、nan、0、0.0、空字符串、空格等统一显示为"0.00"
            
            # 1. 检查pandas的空值类型 - 🎯 [用户需求] 空值也要显示两位小数格式
            if pd.isna(value) or value is None:
                decimal_places = format_config.get('decimal_places', 2)
                return f"{0:.{decimal_places}f}"
            
            # 2. 检查字符串类型的空值情况
            if isinstance(value, str):
                cleaned_str = value.strip()  # 去除前后空格
                # 🎯 [用户需求] 检查空字符串或特殊空值字符串，包括"-"字符
                # 🔧 [P2-修复] 特殊处理车补字段的"-"字符，确保显示为"0.00"
                if (not cleaned_str or 
                    cleaned_str.lower() in ['none', 'nan', 'null', '', ' ', '-'] or
                    (field_name and "车补" in field_name and cleaned_str == '-')):
                    decimal_places = format_config.get('decimal_places', 2)
                    return f"{0:.{decimal_places}f}"
            
            # 3. 转换为浮点数
            try:
                if isinstance(value, str):
                    # 🎯 [用户需求] 特殊处理"-"字符，直接返回"0.00"
                    if value.strip() == '-':
                        decimal_places = format_config.get('decimal_places', 2)
                        return f"{0:.{decimal_places}f}"
                    
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value or cleaned_value == '-':
                        decimal_places = format_config.get('decimal_places', 2)
                        return f"{0:.{decimal_places}f}"
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
                
                # 4. 检查是否为NaN或无穷大
                if pd.isna(numeric_value) or not np.isfinite(numeric_value):
                    decimal_places = format_config.get('decimal_places', 2)
                    return f"{0:.{decimal_places}f}"
                    
            except (ValueError, TypeError, OverflowError):
                decimal_places = format_config.get('decimal_places', 2)
                return f"{0:.{decimal_places}f}"
            
            # 5. 格式化设置 - 🎯 [用户需求] 强制确保所有浮点数字段显示两位小数
            decimal_places = format_config.get('decimal_places', 2)
            
            # 6. 零值处理 - 用户要求0和0.0也显示为"0.00"
            if numeric_value == 0 or numeric_value == 0.0:
                return f"{0:.{decimal_places}f}"  # 确保零值也按decimal_places格式化
            
            # 7. 格式化为指定小数位数 - 🎯 [用户需求] 强制两位小数显示
            formatted_value = f"{numeric_value:.{decimal_places}f}"
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 浮点型值格式化失败: {e}")
            # 🎯 [用户需求] 异常情况下也返回两位小数格式的零值显示
            decimal_places = format_config.get('decimal_places', 2)
            return f"{0:.{decimal_places}f}"
    
    # ================== 百分比渲染 ==================
    
    def _render_percentage_column(self, 
                                 column: pd.Series, 
                                 format_config: Dict, 
                                 field_name: str) -> pd.Series:
        """渲染百分比列"""
        try:
            def format_percentage(value):
                return self._render_percentage_value(value, format_config)
            
            return column.apply(format_percentage)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 百分比列格式化失败 {field_name}: {e}")
            return column
    
    def _render_percentage_value(self, value: Any, format_config: Dict) -> str:
        """渲染百分比值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return "0%"
            
            # 转换为数值
            try:
                if isinstance(value, str):
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return "0%"
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
            except (ValueError, TypeError):
                return "0%"
            
            # 格式化设置
            decimal_places = format_config.get('decimal_places', 1)
            symbol = format_config.get('symbol', '%')
            multiply_by_100 = format_config.get('multiply_by_100', True)
            
            # 计算百分比值
            if multiply_by_100:
                percentage_value = numeric_value * 100
            else:
                percentage_value = numeric_value
            
            # 格式化
            formatted_value = f"{percentage_value:.{decimal_places}f}{symbol}"
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 百分比值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 日期渲染 ==================
    
    def _render_date_column(self, 
                           column: pd.Series, 
                           format_config: Dict, 
                           field_name: str) -> pd.Series:
        """渲染日期列"""
        try:
            def format_date(value):
                return self._render_date_value(value, format_config)
            
            return column.apply(format_date)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 日期列格式化失败 {field_name}: {e}")
            return column
    
    def _render_date_value(self, value: Any, format_config: Dict) -> str:
        """渲染日期值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return ""
            
            # 转换为日期
            try:
                if isinstance(value, str):
                    # 尝试解析字符串日期
                    input_format = format_config.get('input_format', '%Y-%m-%d')
                    date_obj = datetime.strptime(value, input_format)
                elif isinstance(value, (datetime, date)):
                    date_obj = value
                else:
                    return str(value)
            except (ValueError, TypeError):
                return str(value)
            
            # 格式化设置
            display_format = format_config.get('display_format', '%Y年%m月%d日')
            
            # 格式化日期
            formatted_date = date_obj.strftime(display_format)
            
            return formatted_date
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 日期值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 字符串渲染 ==================
    
    def _render_string_column(self, 
                             column: pd.Series, 
                             format_config: Dict, 
                             field_name: str) -> pd.Series:
        """渲染字符串列"""
        try:
            def format_string(value):
                return self._render_string_value(value, format_config, field_name)
            
            return column.apply(format_string)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_string_value(self, value: Any, format_config: Dict, field_name: str = None) -> str:
        """渲染字符串值"""
        try:
            # 🔧 [P1修复] 检查是否错误地传入了Series
            if isinstance(value, pd.Series):
                self.logger.error(f"🔧 [P1修复] _render_string_value错误地接收到Series，字段: {field_name}")
                # 如果是单元素Series，提取值
                if len(value) == 1:
                    value = value.iloc[0]
                else:
                    # 多元素Series，返回第一个值的格式化结果
                    return str(value.iloc[0]) if not value.empty else format_config.get('empty_display', '')
            
            # 🎯 [用户需求2] 增强空值处理 - 处理所有空值类型
            if pd.isna(value) or value is None:
                return format_config.get('empty_display', '')
            
            # 转换为字符串
            str_value = str(value)
            
            # 🎯 [用户需求2] 处理字符串形式的空值
            if isinstance(value, str):
                cleaned_str = value.strip().lower()
                if cleaned_str in ['none', 'nan', 'null', '']:
                    return format_config.get('empty_display', '')
            
            # 🎯 [用户需求2] 检查常见的空值表示
            if str_value.strip().lower() in ['none', 'nan', 'null']:
                return format_config.get('empty_display', '')
            
            # 🎯 [用户需求] 处理浮点数格式的字符串，移除不必要的.0后缀
            # 例如: "19990089.0" -> "19990089"
            if str_value.endswith('.0'):
                try:
                    # 验证这确实是一个整数值
                    float_val = float(str_value)
                    if float_val == int(float_val):  # 确认是整数
                        str_value = str(int(float_val))
                except (ValueError, OverflowError):
                    # 如果转换失败，保持原字符串
                    pass
            
            # 🎯 [用户需求] 人员类别代码特殊处理：数字要显示为两位数（01, 02等）
            if field_name and "人员类别代码" in field_name:
                try:
                    # 尝试转换为整数并零填充到两位
                    if str_value.replace('.', '').replace('-', '').isdigit():
                        numeric_val = int(float(str_value))
                        if 0 <= numeric_val <= 99:  # 合理的人员类别代码范围
                            str_value = f"{numeric_val:02d}"
                except (ValueError, OverflowError):
                    # 如果转换失败，保持原字符串
                    pass
            
            # 格式化设置
            trim_whitespace = format_config.get('trim_whitespace', True)
            max_length = format_config.get('max_length', 100)
            
            # 处理空白字符
            if trim_whitespace:
                str_value = str_value.strip()
            
            # 🎯 [用户需求] 特殊处理"-"字符 - 字符串类型字段中的"-"改为空白
            if str_value == '-':
                return format_config.get('empty_display', '')
            
            # 处理长度限制
            if len(str_value) > max_length:
                str_value = str_value[:max_length-3] + "..."
            
            # 如果处理后为空，返回空值显示
            if not str_value:
                return format_config.get('empty_display', '')
            
            return str_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 字符串值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 月份字符串渲染 ==================
    
    def _render_month_string_column(self, 
                                   column: pd.Series, 
                                   format_config: Dict, 
                                   field_name: str) -> pd.Series:
        """渲染月份字符串列（提取后两位）"""
        try:
            def format_month(value):
                return self._render_month_string_value(value, format_config)
            
            return column.apply(format_month)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 月份字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_month_string_value(self, value: Any, format_config: Dict) -> str:
        """渲染月份字符串值（提取后两位）"""
        try:
            # 处理空值
            if pd.isna(value) or value is None or value == '':
                return ""
            
            # 转换为字符串并提取后两位月份
            try:
                month_str = str(value).strip()
                if not month_str:
                    return ""
                
                # 如果是数字格式，提取后两位
                if month_str.isdigit():
                    if len(month_str) >= 2:
                        return month_str[-2:]
                    else:
                        return month_str.zfill(2)  # 不足两位补零
                else:
                    # 如果包含非数字字符，尝试提取数字部分
                    import re
                    digits = re.findall(r'\d+', month_str)
                    if digits:
                        month_num = digits[-1]  # 取最后一个数字序列
                        if len(month_num) >= 2:
                            return month_num[-2:]
                        else:
                            return month_num.zfill(2)
                    else:
                        return month_str  # 无法提取数字，返回原值
                        
            except (ValueError, TypeError):
                return str(value)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 月份字符串值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 年份字符串渲染 ==================
    
    def _render_year_string_column(self, 
                                  column: pd.Series, 
                                  format_config: Dict, 
                                  field_name: str) -> pd.Series:
        """渲染年份字符串列"""
        try:
            def format_year(value):
                return self._render_year_string_value(value, format_config)
            
            return column.apply(format_year)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 年份字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_year_string_value(self, value: Any, format_config: Dict) -> str:
        """渲染年份字符串值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None or value == '':
                return ""
            
            # 转换为字符串
            try:
                year_str = str(value).strip()
                if not year_str:
                    return ""
                
                # 如果是数字格式，直接返回
                if year_str.isdigit():
                    return year_str
                else:
                    # 如果包含非数字字符，尝试提取数字部分
                    import re
                    digits = re.findall(r'\d+', year_str)
                    if digits:
                        # 通常年份是4位数，选择最可能的年份
                        for digit in digits:
                            if len(digit) == 4 and digit.startswith(('19', '20')):
                                return digit
                        # 如果没有找到4位年份，返回第一个数字序列
                        return digits[0]
                    else:
                        return year_str  # 无法提取数字，返回原值
                        
            except (ValueError, TypeError):
                return str(value)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 年份字符串值格式化失败: {e}")
            return str(value) if value is not None else ""

    # ================== 月份提取后两位字符串渲染 ==================
    
    def _render_month_string_extract_last_two_column(self, 
                                                    column: pd.Series, 
                                                    format_config: Dict, 
                                                    field_name: str) -> pd.Series:
        """渲染月份字符串列（专门提取后两位）"""
        try:
            def format_month_extract_last_two(value):
                return self._render_month_string_extract_last_two_value(value, format_config)
            
            return column.apply(format_month_extract_last_two)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 月份提取后两位字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_month_string_extract_last_two_value(self, value: Any, format_config: Dict) -> str:
        """
        渲染月份字符串值（专门提取后两位并转换为字符串）
        
        🎯 [用户需求] 将字段"月份"提取后两位月份部分，类型转换为字符串
        
        Args:
            value: 原始值（可能是 "2025年5月"、"202505"、"5"等格式）
            format_config: 格式配置
            
        Returns:
            提取后两位的月份字符串（如 "05"、"12"等）
        """
        try:
            # 🎯 [用户需求] 处理空值 - 返回空字符串
            if pd.isna(value) or value is None or value == '':
                return format_config.get('empty_display', '')
            
            # 转换为字符串并清理
            try:
                month_str = str(value).strip()
                if not month_str:
                    return format_config.get('empty_display', '')
                
                # 使用正则表达式提取所有数字
                import re
                digits = re.findall(r'\d+', month_str)
                
                if not digits:
                    # 如果没有找到数字，返回空
                    return format_config.get('empty_display', '')
                
                # 🎯 [用户需求] 提取月份逻辑：
                # 1. 如果有多个数字序列，通常最后一个是月份（如"2025年5月"）
                # 2. 如果只有一个数字序列，根据长度判断
                
                if len(digits) >= 2:
                    # 多个数字序列，取最后一个作为月份
                    month_candidate = digits[-1]
                else:
                    # 单个数字序列
                    month_candidate = digits[0]
                
                # 🎯 [用户需求] 根据数字长度处理：
                if len(month_candidate) >= 6:
                    # 如果是6位或更长（如202505），提取最后两位
                    month_part = month_candidate[-2:]
                elif len(month_candidate) == 4:
                    # 如果是4位（如2025），可能不包含月份信息，返回空
                    return format_config.get('empty_display', '')
                elif len(month_candidate) <= 2:
                    # 如果是1-2位，直接作为月份
                    month_part = month_candidate
                else:
                    # 其他情况（3位、5位等），提取后两位
                    month_part = month_candidate[-2:]
                
                # 🎯 [用户需求] 验证月份合理性（1-12）
                try:
                    month_num = int(month_part)
                    if 1 <= month_num <= 12:
                        # 🎯 [用户需求3] 强制两位数格式，确保"5" -> "05"
                        return month_part.zfill(2)  # 强制补零到两位
                    else:
                        # 月份不合理，返回空
                        return format_config.get('empty_display', '')
                except ValueError:
                    # 无法转换为整数
                    return format_config.get('empty_display', '')
                        
            except (ValueError, TypeError, IndexError):
                return format_config.get('empty_display', '')
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 月份提取后两位字符串值格式化失败: {e}")
            return format_config.get('empty_display', '')

    # ================== 工具和状态方法 ==================
    
    def clear_cache(self):
        """清除格式化缓存"""
        try:
            self._format_cache.clear()
            self.logger.debug("🎨 [格式渲染] 缓存已清除")
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 清除缓存失败: {e}")
    
    def get_rendering_statistics(self) -> Dict[str, Any]:
        """获取渲染统计信息"""
        try:
            return {
                'error_count': self._error_count,
                'warning_count': self._warning_count,
                'cache_size': len(self._format_cache),
                'supported_types': [
                    'currency', 'integer', 'float', 'percentage', 'date', 'string'
                ]
            }
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def validate_data_types(self, df: pd.DataFrame, table_type: str) -> Dict[str, List[str]]:
        """
        验证数据类型
        
        Args:
            df: 数据框
            table_type: 表格类型
            
        Returns:
            验证结果字典 {status: [messages]}
        """
        try:
            result = {
                'valid': [],
                'warnings': [],
                'errors': []
            }
            
            field_types = self.field_registry.get_table_field_types(table_type)
            
            for column in df.columns:
                if column not in field_types:
                    result['warnings'].append(f"未定义字段类型: {column}")
                    continue
                
                field_type = field_types[column]
                
                # 验证数据类型一致性
                if field_type in ['currency', 'integer', 'float']:
                    non_numeric_count = 0
                    for value in df[column].dropna():
                        try:
                            float(str(value).replace(',', '').replace('¥', ''))
                        except (ValueError, TypeError):
                            non_numeric_count += 1
                    
                    if non_numeric_count > 0:
                        result['warnings'].append(
                            f"字段 {column} 包含 {non_numeric_count} 个非数值数据"
                        )
                    else:
                        result['valid'].append(f"字段 {column} 类型验证通过")
                else:
                    result['valid'].append(f"字段 {column} 类型验证通过")
            
            return result

        except Exception as e:
            self.logger.error(f"数据类型验证失败: {e}")
            return {
                'valid': [],
                'warnings': [],
                'errors': [f"验证过程出错: {str(e)}"]
            }

    def _find_mapped_field_name(self, field_name: str, available_columns: List[str], table_type: str) -> Optional[str]:
        """
        🔧 [P1修复] 查找字段名映射，支持双向字段名转换（中文<->英文）

        Args:
            field_name: 要查找的字段名（可能是中文或英文）
            available_columns: 可用的列名列表（可能是英文或中文）
            table_type: 表类型

        Returns:
            映射后的字段名，如果找不到则返回None
        """
        try:
            # 🔧 [P2修复] 双向字段映射表（扩展版本）
            field_mappings = {
                # 中文 -> 英文
                '工号': 'employee_id',
                '姓名': 'employee_name',
                '部门名称': 'department',
                '人员类别': 'employee_type',
                '人员类别代码': 'employee_type_code',
                '2025年岗位工资': 'position_salary_2025',
                '2025年薪级工资': 'grade_salary_2025',
                '津贴': 'allowance',
                '结余津贴': 'balance_allowance',
                '应发工资': 'total_salary',
                '基本工资': 'basic_salary',
                '绩效奖金': 'performance_bonus',
                '加班费': 'overtime_pay',
                '扣款': 'deduction',
                '实发工资': 'net_salary',
                '月份': 'month',
                '补发': 'supplement',
                '借支': 'advance',
                # 英文 -> 中文
                'employee_id': '工号',
                'employee_name': '姓名',
                'department': '部门名称',
                'employee_type': '人员类别',
                'employee_type_code': '人员类别代码',
                'position_salary_2025': '2025年岗位工资',
                'grade_salary_2025': '2025年薪级工资',
                'allowance': '津贴',
                'balance_allowance': '结余津贴',
                'total_salary': '应发工资',
                'basic_salary': '基本工资',
                'performance_bonus': '绩效奖金',
                'overtime_pay': '加班费',
                'deduction': '扣款',
                'net_salary': '实发工资',
                'month': '月份',
                'supplement': '补发',
                'advance': '借支'
            }

            self.logger.debug(f"🔧 [P2修复] 开始字段映射: '{field_name}' -> 可用列数: {len(available_columns)}")

            # 🔧 [P2修复] 1. 直接映射查找（增强日志）
            if field_name in field_mappings:
                mapped_name = field_mappings[field_name]
                if mapped_name in available_columns:
                    self.logger.info(f"🔧 [P2修复] 直接映射成功: '{field_name}' -> '{mapped_name}'")
                    return mapped_name
                else:
                    self.logger.debug(f"🔧 [P2修复] 直接映射目标不在可用列中: '{field_name}' -> '{mapped_name}' (不在 {available_columns[:5]}...)")
            else:
                self.logger.debug(f"🔧 [P2修复] 字段不在映射表中: '{field_name}'")

            # 🔧 [P1修复] 2. 智能模糊匹配：支持关键词匹配
            keyword_mappings = {
                # 中文关键词 -> 英文字段名模式
                '工号': ['employee_id', 'emp_id', 'id'],
                '姓名': ['employee_name', 'name', 'emp_name'],
                '部门': ['department', 'dept'],
                '工资': ['salary', 'pay', 'wage'],
                '津贴': ['allowance', 'subsidy'],
                '奖金': ['bonus', 'award'],
                '扣款': ['deduction', 'deduct'],
                '月份': ['month'],
                # 英文关键词 -> 中文字段名模式
                'employee': ['工号', '姓名', '员工'],
                'salary': ['工资', '薪'],
                'allowance': ['津贴', '补贴'],
                'bonus': ['奖金', '奖励'],
                'department': ['部门'],
                'deduction': ['扣款', '扣除']
            }

            # 查找关键词匹配
            for keyword, patterns in keyword_mappings.items():
                if keyword in field_name:
                    for pattern in patterns:
                        for col in available_columns:
                            if pattern in col:
                                self.logger.debug(f"🔧 [P1修复] 关键词匹配成功: {field_name} -> {col} (关键词: {keyword})")
                                return col

            # 🔧 [P1修复] 3. 部分匹配：查找相似的字段名
            for col in available_columns:
                # 如果字段名的一部分匹配（忽略大小写）
                if field_name.lower() in col.lower() or col.lower() in field_name.lower():
                    # 避免过于宽泛的匹配（如单个字符）
                    if len(field_name) > 1 and len(col) > 1:
                        self.logger.debug(f"🔧 [P1修复] 部分匹配成功: {field_name} -> {col}")
                        return col

            # 🔧 [P1修复] 4. 数字后缀匹配（如"工资1", "工资2"）
            import re
            base_field = re.sub(r'\d+$', '', field_name)  # 移除末尾数字
            if base_field != field_name and base_field in field_mappings:
                mapped_base = field_mappings[base_field]
                # 查找带数字后缀的匹配
                for col in available_columns:
                    if col.startswith(mapped_base):
                        self.logger.debug(f"🔧 [P1修复] 数字后缀匹配成功: {field_name} -> {col}")
                        return col

            return None

        except Exception as e:
            self.logger.error(f"🔧 [P2修复] 字段名映射查找失败: {e}")
            return None

    def _simple_string_match(self, field_name: str, available_columns: List[str]) -> Optional[str]:
        """
        🔧 [P2修复] 简单字符串匹配作为降级处理

        Args:
            field_name: 要匹配的字段名
            available_columns: 可用的列名列表

        Returns:
            匹配的字段名，如果找不到则返回None
        """
        try:
            # 1. 完全匹配（忽略大小写）
            for col in available_columns:
                if field_name.lower() == col.lower():
                    return col

            # 2. 包含匹配（字段名包含在列名中）
            for col in available_columns:
                if field_name in col or col in field_name:
                    return col

            # 3. 去除特殊字符后匹配
            import re
            clean_field = re.sub(r'[^\w]', '', field_name)
            for col in available_columns:
                clean_col = re.sub(r'[^\w]', '', col)
                if clean_field == clean_col:
                    return col

            return None

        except Exception as e:
            self.logger.error(f"🔧 [P2修复] 简单字符串匹配失败: {e}")
            return None
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 数据类型验证失败: {e}")
            return {"errors": [str(e)]}
    
    def reset_statistics(self):
        """重置统计信息"""
        try:
            self._error_count = 0
            self._warning_count = 0
            self.logger.debug("🎨 [格式渲染] 统计信息已重置")
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 重置统计信息失败: {e}")