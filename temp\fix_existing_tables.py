# -*- coding: utf-8 -*-
"""修复已存在的异动表列名中的换行符问题"""
import sys
import os
import sqlite3
import re

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

def clean_column_name(col_name):
    """清理列名，移除换行符但保持中文"""
    # 移除换行符和回车符
    cleaned = col_name.replace('\n', '').replace('\r', '')
    # 将多个连续空格替换为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    # 移除前后空格
    cleaned = cleaned.strip()
    return cleaned

def fix_change_data_tables():
    """修复异动表的列名问题"""
    print("=" * 60)
    print("修复异动表列名中的换行符")
    print("=" * 60)
    
    db_path = 'data/db/salary_system.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取所有异动表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'change_data_%'")
        tables = cursor.fetchall()
        
        print(f"\n找到 {len(tables)} 个异动表需要检查")
        
        for table in tables:
            table_name = table[0]
            print(f"\n处理表: {table_name}")
            
            # 获取列信息
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # 检查是否有需要修复的列名
            columns_to_fix = []
            for col in columns:
                col_name = col[1]
                if '\n' in col_name or '\r' in col_name:
                    cleaned_name = clean_column_name(col_name)
                    columns_to_fix.append((col_name, cleaned_name))
                    print(f"  需要修复: '{col_name}' -> '{cleaned_name}'")
            
            if columns_to_fix:
                print(f"  开始修复表 {table_name}...")
                
                # 创建新表名
                temp_table_name = f"{table_name}_temp"
                
                # 获取完整的列定义
                all_columns = []
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    
                    # 清理列名
                    if '\n' in col_name or '\r' in col_name:
                        cleaned_name = clean_column_name(col_name)
                    else:
                        cleaned_name = col_name
                    
                    all_columns.append(f'"{cleaned_name}" {col_type}')
                
                # 创建新表
                create_sql = f"CREATE TABLE {temp_table_name} ({', '.join(all_columns)})"
                print(f"  创建临时表: {temp_table_name}")
                cursor.execute(create_sql)
                
                # 复制数据
                # 构建列名映射
                old_cols = []
                new_cols = []
                for col in columns:
                    col_name = col[1]
                    old_cols.append(f'"{col_name}"')
                    
                    if '\n' in col_name or '\r' in col_name:
                        cleaned_name = clean_column_name(col_name)
                    else:
                        cleaned_name = col_name
                    new_cols.append(f'"{cleaned_name}"')
                
                # 复制数据到新表
                copy_sql = f"INSERT INTO {temp_table_name} ({', '.join(new_cols)}) SELECT {', '.join(old_cols)} FROM {table_name}"
                print(f"  复制数据到临时表...")
                cursor.execute(copy_sql)
                
                # 删除旧表
                print(f"  删除旧表...")
                cursor.execute(f"DROP TABLE {table_name}")
                
                # 重命名新表
                print(f"  重命名临时表为原表名...")
                cursor.execute(f"ALTER TABLE {temp_table_name} RENAME TO {table_name}")
                
                print(f"  ✓ 表 {table_name} 修复完成")
            else:
                print(f"  ✓ 表 {table_name} 无需修复")
        
        # 提交更改
        conn.commit()
        print(f"\n所有修复完成并已保存")
        
    except Exception as e:
        print(f"\n错误: {e}")
        conn.rollback()
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_change_data_tables()