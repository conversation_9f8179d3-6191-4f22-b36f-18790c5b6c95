"""
测试统一的分页处理流程
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from loguru import logger
import time

def test_unified_pagination():
    """测试统一的分页处理"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = PrototypeMainWindow()
    window.show()
    
    logger.info("="*50)
    logger.info("统一分页处理测试开始")
    logger.info("="*50)
    
    def run_test():
        """运行测试序列"""
        try:
            # 检查分页处理器是否正确初始化
            if not hasattr(window, '_pagination_handler'):
                logger.info("首次使用，分页处理器将延迟加载")
            
            # 模拟分页操作
            logger.info("测试1: 模拟切换到第2页")
            if hasattr(window, '_on_page_changed_new_architecture'):
                start_time = time.time()
                window._on_page_changed_new_architecture(2)
                elapsed = time.time() - start_time
                logger.info(f"分页处理耗时: {elapsed:.3f}秒")
                
                # 检查是否创建了分页处理器
                if hasattr(window, '_pagination_handler'):
                    logger.info("✅ 分页处理器已创建")
                    
                    # 测试缓存
                    logger.info("\n测试2: 再次访问第2页（测试缓存）")
                    start_time = time.time()
                    window._on_page_changed_new_architecture(2)
                    elapsed = time.time() - start_time
                    logger.info(f"缓存分页处理耗时: {elapsed:.3f}秒")
                    
                    if elapsed < 0.1:  # 缓存应该很快
                        logger.info("✅ 缓存机制工作正常")
                    else:
                        logger.warning("⚠️ 缓存可能未生效")
                        
                    # 测试清除缓存
                    logger.info("\n测试3: 清除缓存")
                    window._pagination_handler.clear_cache()
                    logger.info("✅ 缓存已清除")
                    
                else:
                    logger.error("❌ 分页处理器未创建")
            else:
                logger.error("❌ 分页方法不存在")
                
            logger.info("="*50)
            logger.info("统一分页处理测试完成")
            logger.info("="*50)
            
        except Exception as e:
            logger.error(f"测试失败: {e}", exc_info=True)
        finally:
            # 延迟关闭应用
            QTimer.singleShot(3000, app.quit)
    
    # 延迟运行测试，等待窗口初始化
    QTimer.singleShot(1000, run_test)
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_unified_pagination()