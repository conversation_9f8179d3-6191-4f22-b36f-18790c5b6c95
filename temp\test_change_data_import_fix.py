# -*- coding: utf-8 -*-
"""测试异动表数据导入问题"""
import sys
import os
import pandas as pd

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

def test_column_identifier():
    """测试中文列名在SQL中的处理"""
    print("=" * 60)
    print("测试中文列名处理")
    print("=" * 60)
    
    manager = DynamicTableManager()
    
    # 测试_quote_identifier方法
    test_names = [
        '工号',
        '姓名',
        '2025年岗位工资',
        '补发',
        '合计',
        'employee_id',
        'test"quotes"',
        '特殊字符!@#$',
        '空 格',
        '序号'
    ]
    
    print("测试_quote_identifier方法:")
    for name in test_names:
        quoted = manager._quote_identifier(name)
        print(f"  {name} -> {quoted}")
    
    # 测试SQL查询
    print("\n测试SQL查询构建:")
    table_name = "change_data_2025_12_test"
    columns = ['序号', '工号', '姓名', '部门名称', '应发工资']
    
    quoted_columns = [manager._quote_identifier(col) for col in columns]
    placeholders = ', '.join(['?'] * len(columns))
    
    sql = f"INSERT INTO {manager._quote_identifier(table_name)} ({', '.join(quoted_columns)}) VALUES ({placeholders})"
    print(f"生成的SQL: {sql}")
    
    # 测试列名清理
    print("\n测试_clean_column_name方法:")
    for name in test_names:
        cleaned = manager._clean_column_name(name)
        print(f"  {name} -> {cleaned}")
    
    return True

if __name__ == "__main__":
    test_column_identifier()