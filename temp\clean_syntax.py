"""
清理prototype_main_window.py的语法错误
"""
import re

# 读取文件
with open('src/gui/prototype/prototype_main_window.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 找到需要保留的行
keep_until = 4796  # _dummy_method_for_spacing的结束
keep_from = None

# 查找_execute_pagination_data_load的正确定义
for i, line in enumerate(lines, 1):
    if i > 5000 and 'def _execute_pagination_data_load' in line and line.strip().startswith('def'):
        keep_from = i
        break

if keep_from:
    print(f"保留第1-{keep_until}行")
    print(f"删除第{keep_until+1}-{keep_from-1}行")
    print(f"保留第{keep_from}行及之后")
    
    # 创建新内容
    new_lines = lines[:keep_until] + ['\n'] + lines[keep_from-1:]
    
    # 写回文件
    with open('src/gui/prototype/prototype_main_window.py', 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"已清理{keep_from - keep_until - 1}行错误代码")
else:
    print("未找到_execute_pagination_data_load的正确定义")