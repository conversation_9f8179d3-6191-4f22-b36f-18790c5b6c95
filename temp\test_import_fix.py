# -*- coding: utf-8 -*-
"""测试异动表导入修复效果"""
import sys
import os
import pandas as pd
import sqlite3

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

def test_column_cleaning():
    """测试列名清理功能"""
    print("=" * 60)
    print("测试列名清理功能")
    print("=" * 60)
    
    manager = DynamicTableManager()
    
    # 测试包含换行符的列名
    test_names = [
        '基本\n离休费',
        '结余\n津贴',
        '生活\n补贴',
        '增发一次\n性生活补贴',
        '工号',
        '姓名',
        '部门名称'
    ]
    
    print("\n测试_clean_column_name_for_change_data方法:")
    for name in test_names:
        cleaned = manager._clean_column_name_for_change_data(name)
        print(f"  {repr(name)} -> {repr(cleaned)}")
    
    return True

def test_existing_data():
    """测试已修复的数据表"""
    print("\n" + "=" * 60)
    print("测试已修复的数据表")
    print("=" * 60)
    
    conn = sqlite3.connect('data/db/salary_system.db')
    cursor = conn.cursor()
    
    # 检查修复后的表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'change_data_%'")
    tables = cursor.fetchall()
    
    for table in tables:
        table_name = table[0]
        print(f"\n检查表: {table_name}")
        
        # 获取列信息
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        has_newline = False
        for col in columns:
            col_name = col[1]
            if '\n' in col_name or '\r' in col_name:
                print(f"  ⚠️ 仍有换行符: {repr(col_name)}")
                has_newline = True
        
        if not has_newline:
            print(f"  ✓ 所有列名已清理")
            
            # 检查数据
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  数据行数: {count}")
            
            if count > 0:
                # 获取前5行数据
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                rows = cursor.fetchall()
                print(f"  前5行数据预览:")
                
                # 获取列名
                col_names = [col[1] for col in columns]
                
                for i, row in enumerate(rows, 1):
                    print(f"    行{i}:")
                    for j, (col_name, value) in enumerate(zip(col_names[:5], row[:5])):
                        print(f"      {col_name}: {value}")
                    if len(row) > 5:
                        print(f"      ... (共{len(row)}个字段)")
    
    conn.close()
    return True

def test_simulated_import():
    """模拟导入测试"""
    print("\n" + "=" * 60)
    print("模拟导入测试")
    print("=" * 60)
    
    # 创建测试数据，模拟包含换行符的列名
    test_data = {
        '序号': [1, 2, 3],
        '工号': ['001', '002', '003'],
        '姓名': ['张三', '李四', '王五'],
        '部门名称': ['财务部', '人事部', '技术部'],
        '基本\n工资': [5000, 6000, 7000],
        '岗位\n津贴': [1000, 1200, 1500],
        '绩效\n奖金': [2000, 2500, 3000],
        '应发工资': [8000, 9700, 11500]
    }
    
    df = pd.DataFrame(test_data)
    print(f"\n创建测试DataFrame，包含换行符的列名:")
    print(f"原始列名: {list(df.columns)}")
    
    # 测试列名清理
    manager = DynamicTableManager()
    cleaned_columns = {}
    for col in df.columns:
        cleaned = manager._clean_column_name_for_change_data(col)
        cleaned_columns[col] = cleaned
    
    print(f"\n清理后的列名映射:")
    for old, new in cleaned_columns.items():
        if old != new:
            print(f"  {repr(old)} -> {repr(new)}")
    
    # 应用列名清理
    df_cleaned = df.rename(columns=cleaned_columns)
    print(f"\n清理后的列名: {list(df_cleaned.columns)}")
    
    return True

if __name__ == "__main__":
    try:
        # 运行测试
        print("开始测试异动表导入修复效果...\n")
        
        success = True
        
        # 测试1: 列名清理功能
        if not test_column_cleaning():
            success = False
            print("❌ 列名清理功能测试失败")
        else:
            print("✓ 列名清理功能测试通过")
        
        # 测试2: 已修复的数据表
        if not test_existing_data():
            success = False
            print("❌ 数据表检查测试失败")
        else:
            print("✓ 数据表检查测试通过")
        
        # 测试3: 模拟导入
        if not test_simulated_import():
            success = False
            print("❌ 模拟导入测试失败")
        else:
            print("✓ 模拟导入测试通过")
        
        print("\n" + "=" * 60)
        if success:
            print("✅ 所有测试通过！异动表导入问题已修复")
        else:
            print("❌ 部分测试失败，请检查日志")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()