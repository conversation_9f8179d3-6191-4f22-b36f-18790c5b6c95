# -*- coding: utf-8 -*-
"""检查数据库中的列名问题"""
import sys
import os
import sqlite3

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

def check_columns():
    """检查异动表的列名"""
    print("=" * 60)
    print("检查异动表列名")
    print("=" * 60)
    
    conn = sqlite3.connect('data/db/salary_system.db')
    cursor = conn.cursor()
    
    # 获取所有异动表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'change_data_%'")
    tables = cursor.fetchall()
    
    print(f"找到 {len(tables)} 个异动表")
    
    for table in tables:
        table_name = table[0]
        print(f"\n表: {table_name}")
        
        # 获取列信息
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print(f"  列数: {len(columns)}")
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            # 检查是否包含换行符或其他特殊字符
            if '\n' in col_name or '\r' in col_name:
                print(f"  ⚠️ 列名包含换行符: {repr(col_name)} ({col_type})")
            elif ' ' in col_name:
                print(f"  ℹ️ 列名包含空格: {repr(col_name)} ({col_type})")
            else:
                print(f"  ✓ {col_name} ({col_type})")
    
    conn.close()

if __name__ == "__main__":
    check_columns()