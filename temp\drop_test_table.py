# -*- coding: utf-8 -*-
"""删除测试表"""
import sys
import os
import sqlite3

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

def drop_table():
    """删除测试表"""
    print("=" * 60)
    print("删除测试表")
    print("=" * 60)
    
    conn = sqlite3.connect('data/db/salary_system.db')
    cursor = conn.cursor()
    
    table_name = "change_data_2025_12_test_import"
    
    try:
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        conn.commit()
        print(f"✓ 表 {table_name} 已删除")
    except Exception as e:
        print(f"❌ 删除失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    drop_table()