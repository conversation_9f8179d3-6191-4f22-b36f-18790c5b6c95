# -*- coding: utf-8 -*-
import sqlite3
import json
import sys
sys.stdout.reconfigure(encoding='utf-8')

# 连接数据库
conn = sqlite3.connect('data/db/salary_system.db')
cursor = conn.cursor()

# 获取所有表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
all_tables = cursor.fetchall()
print("所有表:")
for table in all_tables:
    print(f"  - {table[0]}")

# 获取所有异动表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'change_data_%'")
tables = cursor.fetchall()
print("\n找到的异动表:")
for table in tables:
    print(f"  - {table[0]}")

# 检查具体表的列信息
if tables:
    for table_name in tables[:3]:  # 只检查前3个表
        table_name = table_name[0]
        print(f"\n表 {table_name} 的列信息:")
        cursor.execute(f"PRAGMA table_info({table_name})")
        cols = cursor.fetchall()
        for col in cols:
            print(f"  {col[1]} ({col[2]})")  # 列名和类型
        
        # 获取前2行数据看看
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 2")
        rows = cursor.fetchall()
        print(f"  前{len(rows)}行数据:")
        for row in rows:
            print(f"    {row[:5]}...")  # 只显示前5列

conn.close()