# -*- coding: utf-8 -*-
"""检查测试表的结构"""
import sys
import os
import sqlite3

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

def check_table():
    """检查测试表结构"""
    print("=" * 60)
    print("检查测试表结构")
    print("=" * 60)
    
    conn = sqlite3.connect('data/db/salary_system.db')
    cursor = conn.cursor()
    
    table_name = "change_data_2025_12_test_import"
    
    # 检查表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    if not cursor.fetchone():
        print(f"表 {table_name} 不存在")
        conn.close()
        return
    
    print(f"\n表 {table_name} 的结构:")
    
    # 获取列信息
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    print(f"列数: {len(columns)}\n")
    print("列信息:")
    print("-" * 60)
    print(f"{'序号':<5} {'列名':<20} {'类型':<10} {'NOT NULL':<10} {'默认值':<10} {'主键':<5}")
    print("-" * 60)
    
    for col in columns:
        cid, name, col_type, not_null, default_val, pk = col
        not_null_str = "是" if not_null else "否"
        pk_str = "是" if pk else "否"
        default_str = str(default_val) if default_val is not None else "NULL"
        print(f"{cid:<5} {name:<20} {col_type:<10} {not_null_str:<10} {default_str:<10} {pk_str:<5}")
    
    print("-" * 60)
    
    # 检查数据
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    print(f"\n数据行数: {count}")
    
    conn.close()

if __name__ == "__main__":
    check_table()