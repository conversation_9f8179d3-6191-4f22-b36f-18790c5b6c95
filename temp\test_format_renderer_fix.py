"""
测试格式渲染修复
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from src.modules.format_management.format_renderer import FormatRenderer
from loguru import logger

def test_format_renderer():
    """测试格式渲染器修复"""
    logger.info("="*50)
    logger.info("格式渲染修复测试开始")
    logger.info("="*50)
    
    # 创建测试数据
    test_data = {
        '姓名': ['张三', '李四', '王五'],
        '月份': ['202512', '202512', '202512'],
        '补发': [1000.50, 2000.00, None],
        '借支': [500, 0, 300],
        '工号': ['001', '002', '003']
    }
    
    df = pd.DataFrame(test_data)
    logger.info(f"测试数据创建完成: {df.shape[0]}行, {df.shape[1]}列")
    
    # 创建格式渲染器（需要配置对象）
    from src.modules.format_management.format_config import FormatConfig
    from src.modules.format_management.field_registry import FieldRegistry
    
    format_config = FormatConfig('state/data/format_config.json')
    field_registry = FieldRegistry('state/data/field_mappings.json')
    renderer = FormatRenderer(format_config, field_registry)
    
    # 测试渲染
    try:
        # 测试单个值渲染（应该不会报错）
        test_value = df['补发'].iloc[0]
        result = renderer._render_float_value(test_value, {'decimal_places': 2})
        logger.info(f"单值渲染成功: {test_value} -> {result}")
        
        # 测试Series渲染（之前会报错，现在应该处理了）
        test_series = df['补发']
        logger.info(f"测试Series类型: {type(test_series)}")
        
        # 模拟错误传递Series的情况
        try:
            result = renderer._render_string_value(test_series, {}, '测试字段')
            logger.warning(f"Series被错误传递，但已处理: {result}")
        except Exception as e:
            logger.error(f"Series处理失败: {e}")
        
        # 测试整个DataFrame渲染
        rendered_df = renderer.render_dataframe(df, 'active_employees')
        logger.info(f"DataFrame渲染成功: {rendered_df.shape}")
        
        logger.info("="*50)
        logger.info("✅ 格式渲染修复测试通过")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)
        logger.info("="*50)
        logger.info("格式渲染修复测试失败")
        logger.info("="*50)

if __name__ == "__main__":
    test_format_renderer()