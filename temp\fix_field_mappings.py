"""
修复字段映射配置
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from src.core.field_mapping_manager import FieldMappingManager
from loguru import logger

def fix_field_mappings():
    """修复现有的字段映射配置"""
    logger.info("="*50)
    logger.info("开始修复字段映射配置")
    logger.info("="*50)
    
    # 创建字段映射管理器
    manager = FieldMappingManager()
    
    # 需要修复的表列表
    tables_to_fix = [
        "change_data_2025_12_active_employees",
        "change_data_2025_12_retired_employees",
        "change_data_2025_12_pension_employees",
        "change_data_2025_12_a_grade_employees",
        "active_employees",
        "salary_data_2025_07_active_employees"
    ]
    
    for table_name in tables_to_fix:
        logger.info(f"\n处理表: {table_name}")
        
        # 确保映射完整
        success = manager.ensure_table_mapping_complete(table_name)
        
        if success:
            # 获取更新后的配置
            mapping = manager.get_field_mapping(table_name)
            field_types = manager.get_field_types(table_name)
            hidden_fields = manager.get_hidden_fields(table_name)
            
            logger.info(f"  ✅ 字段映射: {len(mapping) if mapping else 0} 个字段")
            logger.info(f"  ✅ 字段类型: {len(field_types)} 个类型")
            logger.info(f"  ✅ 隐藏字段: {len(hidden_fields)} 个")
            
            # 验证示例数据列
            sample_columns = [
                "employee_id", "employee_name", "department",
                "position_salary_2025", "total_salary"
            ]
            
            validation = manager.validate_mapping(table_name, sample_columns)
            if validation.get("is_valid"):
                logger.info(f"  ✅ 映射验证通过")
            else:
                logger.warning(f"  ⚠️ 映射验证警告: {validation}")
        else:
            logger.error(f"  ❌ 修复失败")
    
    # 重新加载并显示配置摘要
    manager.reload_mappings()
    
    logger.info("\n" + "="*50)
    logger.info("字段映射修复完成")
    logger.info("="*50)
    
    # 读取并显示配置文件统计
    try:
        with open('state/data/field_mappings.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        logger.info(f"\n配置文件统计:")
        logger.info(f"  版本: {config.get('version', 'unknown')}")
        logger.info(f"  表数量: {len(config.get('table_mappings', {}))}")
        
        for table_name, table_config in config.get('table_mappings', {}).items():
            has_mappings = 'field_mappings' in table_config
            has_types = 'field_types' in table_config
            has_hidden = 'hidden_fields' in table_config
            has_order = 'display_order' in table_config
            
            status = "✅" if all([has_mappings, has_types, has_hidden, has_order]) else "⚠️"
            logger.info(f"  {status} {table_name}: "
                       f"映射={'✓' if has_mappings else '✗'} "
                       f"类型={'✓' if has_types else '✗'} "
                       f"隐藏={'✓' if has_hidden else '✗'} "
                       f"顺序={'✓' if has_order else '✗'}")
                       
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")

if __name__ == "__main__":
    fix_field_mappings()