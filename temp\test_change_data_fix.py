# -*- coding: utf-8 -*-
"""测试异动表数据导入修复效果"""
import sys
import os
import pandas as pd

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from pathlib import Path

def test_change_data_import():
    """测试异动表数据导入"""
    print("=" * 60)
    print("测试异动表数据导入修复")
    print("=" * 60)
    
    # 创建测试数据 - 模拟Excel导入的数据
    test_data = {
        '序号': [1, 2, 3],
        '工号': ['TEST001', 'TEST002', 'TEST003'],
        '姓名': ['测试张三', '测试李四', '测试王五'],
        '部门名称': ['测试部门A', '测试部门B', '测试部门C'],
        '人员类别': ['在职', '在职', '在职'],
        '人员类别代码': ['1', '1', '1'],
        '2025年岗位工资': [5000, 6000, 5500],
        '2025年薪级工资': [3000, 3500, 3200],
        '津贴': [100, 150, 120],
        '结余津贴': [50, 60, 55],
        '2025年基础性绩效': [2000, 2200, 2100],
        '卫生费': [30, 30, 30],
        '交通补贴': [200, 200, 200],
        '物业补贴': [100, 100, 100],
        '住房补贴': [500, 500, 500],
        '车补': [0, 300, 0],
        '通讯补贴': [100, 100, 100],
        '2025年奖励性绩效预发': [3000, 3500, 3200],
        '补发': [0, 0, 0],
        '借支': [0, 0, 0],
        '应发工资': [14080, 16640, 15105],
        '2025公积金': [1200, 1400, 1300],
        '代扣代存养老保险': [800, 900, 850]
    }
    
    df = pd.DataFrame(test_data)
    print(f"测试数据准备完成: {len(df)} 行, {len(df.columns)} 列")
    print(f"数据列: {df.columns.tolist()[:5]}...")
    
    # 初始化表管理器
    manager = DynamicTableManager()
    
    # 测试表名
    test_table = "change_data_2025_12_test"
    
    try:
        # 保存数据到异动表
        print(f"\n开始保存数据到表: {test_table}")
        success, message = manager.save_dataframe_to_table(df, test_table)
        
        if success:
            print(f"✅ 保存成功: {message}")
            
            # 验证数据
            print("\n验证保存的数据:")
            verify_sql = f"SELECT COUNT(*) FROM {test_table}"
            result = manager.db_manager.execute_query(verify_sql)
            count = result[0][0] if result else 0
            print(f"  记录数: {count}")
            
            # 查询前2条数据
            sample_sql = f"SELECT 工号, 姓名, 部门名称, 应发工资 FROM {test_table} LIMIT 2"
            samples = manager.db_manager.execute_query(sample_sql)
            if samples:
                print("  前2条记录:")
                for i, row in enumerate(samples):
                    print(f"    {i+1}. 工号:{row[0]}, 姓名:{row[1]}, 部门:{row[2]}, 应发:{row[3]}")
            else:
                print("  ❌ 无法查询到数据")
                
            # 清理测试表
            print(f"\n清理测试表: {test_table}")
            manager.db_manager.execute_update(f"DROP TABLE IF EXISTS {test_table}")
            print("✅ 测试表已清理")
            
        else:
            print(f"❌ 保存失败: {message}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理测试表
        try:
            manager.db_manager.execute_update(f"DROP TABLE IF EXISTS {test_table}")
        except:
            pass
    
    return success

if __name__ == "__main__":
    test_change_data_import()