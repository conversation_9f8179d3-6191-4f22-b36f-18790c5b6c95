"""
测试分页功能修复
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from loguru import logger

def test_pagination():
    """测试分页功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = PrototypeMainWindow()
    window.show()
    
    logger.info("="*50)
    logger.info("分页功能测试开始")
    logger.info("="*50)
    logger.info("测试步骤：")
    logger.info("1. 导航到'异动人员表'标签")
    logger.info("2. 导入异动表数据")
    logger.info("3. 点击下一页按钮")
    logger.info("4. 观察表头是否正常显示")
    logger.info("="*50)
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_pagination()