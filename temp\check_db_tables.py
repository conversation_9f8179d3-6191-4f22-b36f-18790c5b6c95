# -*- coding: utf-8 -*-
"""检查数据库中的所有表"""
import sys
import os
import sqlite3

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
sys.stdout.reconfigure(encoding='utf-8')

def check_tables():
    """检查数据库中的所有表"""
    print("=" * 60)
    print("检查数据库中的所有表")
    print("=" * 60)
    
    conn = sqlite3.connect('data/db/salary_system.db')
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = cursor.fetchall()
    
    print(f"\n找到 {len(tables)} 个表:")
    print("-" * 60)
    
    # 按类型分组
    salary_tables = []
    change_tables = []
    other_tables = []
    
    for table in tables:
        table_name = table[0]
        if table_name.startswith('salary_data_'):
            salary_tables.append(table_name)
        elif table_name.startswith('change_data_'):
            change_tables.append(table_name)
        else:
            other_tables.append(table_name)
    
    # 显示分组结果
    if salary_tables:
        print(f"\n工资表 ({len(salary_tables)} 个):")
        for t in salary_tables:
            # 获取行数
            cursor.execute(f"SELECT COUNT(*) FROM {t}")
            count = cursor.fetchone()[0]
            print(f"  - {t}: {count} 行")
    
    if change_tables:
        print(f"\n异动表 ({len(change_tables)} 个):")
        for t in change_tables:
            # 获取行数
            cursor.execute(f"SELECT COUNT(*) FROM {t}")
            count = cursor.fetchone()[0]
            print(f"  - {t}: {count} 行")
    
    if other_tables:
        print(f"\n其他表 ({len(other_tables)} 个):")
        for t in other_tables:
            print(f"  - {t}")
    
    conn.close()

if __name__ == "__main__":
    check_tables()