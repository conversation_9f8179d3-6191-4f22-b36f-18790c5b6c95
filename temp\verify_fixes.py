"""
验证 P0-P3 所有修复
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger

def verify_fixes():
    """验证所有修复"""
    logger.info("="*60)
    logger.info("P0-P3 修复验证报告")
    logger.info("="*60)
    
    results = []
    
    # P0 - 分页标志管理
    try:
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        # 检查方法是否存在
        if hasattr(PrototypeMainWindow, '_on_page_changed_new_architecture'):
            results.append(("P0 - 分页标志管理", "已修复", "prototype_main_window.py"))
        else:
            results.append(("P0 - 分页标志管理", "未找到", ""))
    except Exception as e:
        results.append(("P0 - 分页标志管理", f"错误: {e}", ""))
    
    # P1 - 格式渲染器
    try:
        from src.modules.format_management.format_renderer import FormatRenderer
        import pandas as pd
        
        renderer = FormatRenderer(
            format_config_path='config/format_config.json',
            field_registry_config_path='config/field_registry.json'
        )
        
        # 测试Series处理
        test_series = pd.Series([1000.0])
        result = renderer._render_float_value(test_series, "test", {})
        results.append(("P1 - 格式渲染器Series处理", "已修复", "format_renderer.py"))
        
    except Exception as e:
        results.append(("P1 - 格式渲染器Series处理", f"错误: {e}", ""))
    
    # P2 - 统一分页处理器
    try:
        from src.gui.prototype.pagination_handler import PaginationHandler
        results.append(("P2 - 统一分页处理器", "已实现", "pagination_handler.py"))
    except Exception as e:
        results.append(("P2 - 统一分页处理器", f"错误: {e}", ""))
    
    # P3 - 字段映射管理器
    try:
        from src.core.field_mapping_manager import FieldMappingManager
        manager = FieldMappingManager()
        
        # 测试基本功能
        test_table = "change_data_2025_12_active_employees"
        mapping = manager.get_field_mapping(test_table)
        
        if mapping:
            results.append(("P3 - 字段映射管理器", "已完成", "field_mapping_manager.py"))
        else:
            results.append(("P3 - 字段映射管理器", "无映射", ""))
            
    except Exception as e:
        results.append(("P3 - 字段映射管理器", f"错误: {e}", ""))
    
    # 输出结果
    logger.info("\n修复状态:")
    logger.info("-" * 50)
    
    success_count = 0
    for fix_name, status, file_name in results:
        if "已" in status:
            success_count += 1
            logger.info(f"[成功] {fix_name}: {status}")
            if file_name:
                logger.info(f"       文件: {file_name}")
        else:
            logger.error(f"[失败] {fix_name}: {status}")
    
    logger.info("-" * 50)
    logger.info(f"总计: {success_count}/{len(results)} 项修复已验证")
    
    if success_count == len(results):
        logger.info("\n所有 P0-P3 级问题已成功修复!")
        logger.info("系统现在应该能够:")
        logger.info("1. 正确处理分页时的表头显示")
        logger.info("2. 避免格式渲染的 Series 错误")
        logger.info("3. 使用统一的分页处理逻辑")
        logger.info("4. 自动管理异动表的字段映射")
    else:
        logger.warning(f"\n还有 {len(results) - success_count} 项需要检查")
    
    logger.info("="*60)

if __name__ == "__main__":
    verify_fixes()